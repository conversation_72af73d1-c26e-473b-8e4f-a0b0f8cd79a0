<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HomeBible - Your Digital Spiritual Companion</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 0;
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            backdrop-filter: blur(10px);
        }

        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.8rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: opacity 0.3s;
        }

        .nav-links a:hover {
            opacity: 0.8;
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 120px 0 80px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="1" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="1" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero h1 {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero p {
            font-size: 1.3rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .cta-button {
            display: inline-block;
            background: white;
            color: #667eea;
            padding: 15px 40px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.3);
        }

        /* Features Section */
        .features {
            padding: 100px 0;
            background: #f8fafc;
        }

        .section-title {
            text-align: center;
            margin-bottom: 4rem;
        }

        .section-title h2 {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 1rem;
        }

        .section-title p {
            font-size: 1.2rem;
            color: #718096;
            max-width: 600px;
            margin: 0 auto;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 3rem;
            margin-top: 4rem;
        }

        .feature-card {
            background: white;
            padding: 3rem 2rem;
            border-radius: 20px;
            text-align: center;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            transition: all 0.3s;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 60px rgba(0,0,0,0.15);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2rem;
            font-size: 2rem;
            color: white;
        }

        .feature-card h3 {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #2d3748;
        }

        .feature-card p {
            color: #718096;
            line-height: 1.7;
        }

        /* App Preview Section */
        .app-preview {
            padding: 100px 0;
            background: white;
        }

        .preview-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
        }

        .preview-text h2 {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 2rem;
        }

        .preview-text p {
            font-size: 1.1rem;
            color: #718096;
            margin-bottom: 2rem;
            line-height: 1.7;
        }

        .download-buttons {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .download-btn {
            display: flex;
            align-items: center;
            gap: 10px;
            background: #2d3748;
            color: white;
            padding: 12px 24px;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s;
        }

        .download-btn:hover {
            background: #4a5568;
            transform: translateY(-2px);
        }

        .coming-soon {
            background: #718096 !important;
            cursor: default;
            position: relative;
        }

        .coming-soon:hover {
            background: #718096 !important;
            transform: none !important;
        }

        .phone-mockup {
            text-align: center;
            position: relative;
        }

        .phone-frame {
            width: 300px;
            height: 600px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 40px;
            margin: 0 auto;
            padding: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            position: relative;
        }

        .phone-screen {
            width: 100%;
            height: 100%;
            background: white;
            border-radius: 30px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: #667eea;
            font-weight: 600;
        }

        /* Footer */
        footer {
            background: #2d3748;
            color: white;
            padding: 3rem 0;
            text-align: center;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-section h3 {
            margin-bottom: 1rem;
            font-weight: 600;
        }

        .footer-section a {
            color: #a0aec0;
            text-decoration: none;
            display: block;
            margin-bottom: 0.5rem;
            transition: color 0.3s;
        }

        .footer-section a:hover {
            color: white;
        }

        .footer-bottom {
            border-top: 1px solid #4a5568;
            padding-top: 2rem;
            color: #a0aec0;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .nav-links {
                display: none;
            }
            
            .preview-content {
                grid-template-columns: 1fr;
                text-align: center;
            }
            
            .phone-frame {
                width: 250px;
                height: 500px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header>
        <nav class="container">
            <div class="logo">
                <i class="fas fa-book-open"></i>
                HomeBible
            </div>
            <ul class="nav-links">
                <li><a href="#features">Features</a></li>
                <li><a href="#download">Coming Soon</a></li>
                <li><a href="#contact">Contact</a></li>
            </ul>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <h1>Your Digital Spiritual Companion</h1>
                <p>Experience daily prayers, devotions, Bible reading, and spiritual growth with HomeBible - the comprehensive app for your faith journey.</p>
                <a href="#download" class="cta-button">
                    <i class="fas fa-bell"></i> Get Notified
                </a>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features" id="features">
        <div class="container">
            <div class="section-title">
                <h2>Powerful Features for Your Faith</h2>
                <p>Discover the comprehensive tools designed to enrich your spiritual life and deepen your connection with God.</p>
            </div>
            
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-praying-hands"></i>
                    </div>
                    <h3>Morning & Evening Prayers</h3>
                    <p>Start and end your day with guided prayers, creating a consistent spiritual rhythm that brings peace and purpose to your daily life.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-heart"></i>
                    </div>
                    <h3>Daily Devotions</h3>
                    <p>Immerse yourself in thoughtful devotional content that inspires reflection, growth, and a deeper understanding of your faith.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-book-bible"></i>
                    </div>
                    <h3>Bible Reading Plans</h3>
                    <p>Follow structured reading plans that guide you through Scripture systematically, helping you build a consistent habit of Bible study.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-question-circle"></i>
                    </div>
                    <h3>Interactive Quizzes</h3>
                    <p>Test your biblical knowledge and reinforce your learning with engaging quizzes that make studying Scripture both fun and educational.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- App Preview Section -->
    <section class="app-preview" id="download">
        <div class="container">
            <div class="preview-content">
                <div class="preview-text">
                    <h2>Take Your Faith Journey Anywhere</h2>
                    <p>HomeBible brings the power of spiritual growth to your fingertips. Whether you're at home, traveling, or taking a quiet moment during your busy day, access prayers, devotions, and Scripture study tools whenever you need them.</p>
                    <p>We're preparing to launch HomeBible on Google Play to help believers worldwide strengthen their faith through digital tools.</p>
                    
                    <div class="download-buttons">
                        <a href="#" class="download-btn coming-soon">
                            <i class="fab fa-google-play"></i>
                            <div>
                                <div style="font-size: 0.8rem;">Coming Soon on</div>
                                <div>Google Play</div>
                            </div>
                        </a>
                    </div>
                </div>
                
                <div class="phone-mockup">
                    <div class="phone-frame">
                        <div class="phone-screen">
                            <i class="fas fa-mobile-alt" style="font-size: 4rem; margin-bottom: 1rem; opacity: 0.3;"></i>
                            <div>HomeBible App</div>
                            <div style="font-size: 0.9rem; opacity: 0.7;">Launching Soon on Google Play</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer id="contact">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>HomeBible</h3>
                    <p>Your digital companion for spiritual growth and biblical study.</p>
                </div>
                
                <div class="footer-section">
                    <h3>Features</h3>
                    <a href="#">Morning Prayers</a>
                    <a href="#">Evening Prayers</a>
                    <a href="#">Daily Devotions</a>
                    <a href="#">Bible Reading</a>
                    <a href="#">Interactive Quizzes</a>
                </div>
                
                <div class="footer-section">
                    <h3>Support</h3>
                    <a href="#">Help Center</a>
                    <a href="#">Contact Us</a>
                    <a href="#">Privacy Policy</a>
                    <a href="#">Terms of Service</a>
                </div>
                
                <div class="footer-section">
                    <h3>Connect</h3>
                    <a href="#"><i class="fab fa-facebook"></i> Facebook</a>
                    <a href="#"><i class="fab fa-twitter"></i> Twitter</a>
                    <a href="#"><i class="fab fa-instagram"></i> Instagram</a>
                    <a href="#"><i class="fab fa-youtube"></i> YouTube</a>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 HomeBible. All rights reserved. | Bringing faith to the digital age.</p>
            </div>
        </div>
    </footer>

    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add scroll effect to header
        window.addEventListener('scroll', () => {
            const header = document.querySelector('header');
            if (window.scrollY > 100) {
                header.style.background = 'rgba(102, 126, 234, 0.95)';
            } else {
                header.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
            }
        });
    </script>
</body>
</html>
